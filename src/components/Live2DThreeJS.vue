<template>
  <div class="live2d-threejs-container">
    <div class="control-header">
      <h3>🌟 Three.js 3D 渲染</h3>
      <button
        @click="toggleThreeJSMode"
        :class="['toggle-btn', { active: isThreeJSMode }]"
      >
        {{ isThreeJSMode ? '🎮 Three.js模式' : '🖼️ PIXI模式' }}
      </button>
    </div>

    <div v-if="isThreeJSMode" class="threejs-controls">
      <!-- Three.js 渲染画布 -->
      <div class="canvas-container">
        <canvas ref="threeCanvas" class="threejs-canvas" width="600" height="600"></canvas>
        <div class="canvas-overlay">
          <div class="info-panel">
            <p>FPS: {{ fps }}</p>
            <p>模式: Three.js 3D</p>
          </div>
        </div>
      </div>

      <!-- 3D 控制面板 -->
      <div class="controls-panel">
        <div class="control-group">
          <h4>🎥 相机控制</h4>
          <div class="control-item">
            <label>相机距离: {{ cameraDistance }}</label>
            <input
              type="range"
              v-model.number="cameraDistance"
              min="5"
              max="50"
              step="1"
              @input="updateCamera"
            />
          </div>
          <div class="control-item">
            <label>相机高度: {{ cameraHeight }}</label>
            <input
              type="range"
              v-model.number="cameraHeight"
              min="-10"
              max="10"
              step="0.5"
              @input="updateCamera"
            />
          </div>
        </div>

        <div class="control-group">
          <h4>🔄 模型变换</h4>
          <div class="control-item">
            <label>X轴旋转: {{ modelRotationX.toFixed(2) }}</label>
            <input
              type="range"
              v-model.number="modelRotationX"
              min="-3.14"
              max="3.14"
              step="0.1"
              @input="updateModelTransform"
            />
          </div>
          <div class="control-item">
            <label>Y轴旋转: {{ modelRotationY.toFixed(2) }}</label>
            <input
              type="range"
              v-model.number="modelRotationY"
              min="-3.14"
              max="3.14"
              step="0.1"
              @input="updateModelTransform"
            />
          </div>
          <div class="control-item">
            <label>Z轴旋转: {{ modelRotationZ.toFixed(2) }}</label>
            <input
              type="range"
              v-model.number="modelRotationZ"
              min="-3.14"
              max="3.14"
              step="0.1"
              @input="updateModelTransform"
            />
          </div>
        </div>

        <div class="control-group">
          <h4>💡 光照效果</h4>
          <div class="control-item">
            <label>环境光强度: {{ ambientLightIntensity.toFixed(2) }}</label>
            <input
              type="range"
              v-model.number="ambientLightIntensity"
              min="0"
              max="2"
              step="0.1"
              @input="updateLighting"
            />
          </div>
          <div class="control-item">
            <label>方向光强度: {{ directionalLightIntensity.toFixed(2) }}</label>
            <input
              type="range"
              v-model.number="directionalLightIntensity"
              min="0"
              max="3"
              step="0.1"
              @input="updateLighting"
            />
          </div>
        </div>

        <div class="control-group">
          <h4>🎬 动画控制</h4>
          <div class="animation-buttons">
            <button @click="startOrbitAnimation" :class="{ active: isOrbiting }">
              {{ isOrbiting ? '⏸️ 停止环绕' : '🔄 环绕动画' }}
            </button>
            <button @click="resetView" class="reset-btn">
              🔄 重置视角
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as THREE from 'three'

// Props
const props = defineProps({
  pixiApp: Object,
  live2dModel: Object
})

// Emits
const emit = defineEmits(['modeChanged'])

// 响应式状态
const isThreeJSMode = ref(false)
const threeCanvas = ref(null)
const fps = ref(0)

// Three.js 相关对象
let scene = null
let camera = null
let renderer = null
let animationId = null
let modelMesh = null

// 控制参数
const cameraDistance = ref(15)
const cameraHeight = ref(0)
const modelRotationX = ref(0)
const modelRotationY = ref(0)
const modelRotationZ = ref(0)
const ambientLightIntensity = ref(0.6)
const directionalLightIntensity = ref(1.0)
const isOrbiting = ref(false)

// 光照对象
let ambientLight = null
let directionalLight = null

// 动画控制
let orbitAnimation = null
let lastTime = 0

onMounted(() => {
  console.log('Live2DThreeJS 组件已挂载')
})

onUnmounted(() => {
  cleanup()
})

// 监听模式切换
watch(isThreeJSMode, (newValue) => {
  if (newValue) {
    initThreeJS()
  } else {
    cleanup()
  }
  emit('modeChanged', newValue)
})

/**
 * 切换 Three.js 模式
 */
function toggleThreeJSMode() {
  isThreeJSMode.value = !isThreeJSMode.value
}

/**
 * 初始化 Three.js 场景
 */
function initThreeJS() {
  // 等待下一帧确保 DOM 元素已渲染
  setTimeout(() => {
    if (!threeCanvas.value) {
      console.error('Three.js canvas 元素未找到')
      return
    }

    console.log('初始化 Three.js 场景...')

    // 创建场景
    scene = new THREE.Scene()
    scene.background = new THREE.Color(0x000000)

    // 创建相机
    camera = new THREE.PerspectiveCamera(
      75, // 视野角度
      600 / 600, // 宽高比
      0.1, // 近裁剪面
      1000 // 远裁剪面
    )

    // 创建渲染器
    renderer = new THREE.WebGLRenderer({
      canvas: threeCanvas.value,
      antialias: true,
      alpha: true
    })
    renderer.setSize(600, 600)
    renderer.setPixelRatio(window.devicePixelRatio)
    renderer.shadowMap.enabled = true
    renderer.shadowMap.type = THREE.PCFSoftShadowMap

    // 设置相机位置
    updateCamera()

    // 创建光照
    setupLighting()

    // 创建 Live2D 模型的 3D 表示
    createModelMesh()

    // 开始渲染循环
    startRenderLoop()

    console.log('Three.js 场景初始化完成')
  }, 100)
}

/**
 * 设置光照
 */
function setupLighting() {
  // 环境光
  ambientLight = new THREE.AmbientLight(0xffffff, ambientLightIntensity.value)
  scene.add(ambientLight)

  // 方向光
  directionalLight = new THREE.DirectionalLight(0xffffff, directionalLightIntensity.value)
  directionalLight.position.set(5, 5, 5)
  directionalLight.castShadow = true
  directionalLight.shadow.mapSize.width = 2048
  directionalLight.shadow.mapSize.height = 2048
  scene.add(directionalLight)
}

/**
 * 创建模型网格
 */
function createModelMesh() {
  // 创建一个平面几何体来承载 Live2D 纹理
  const geometry = new THREE.PlaneGeometry(8, 10)
  
  // 如果有 PIXI 应用，尝试获取其渲染纹理
  let texture
  if (props.pixiApp && props.pixiApp.renderer) {
    // 创建渲染纹理
    const renderTexture = THREE.CanvasTexture.fromCanvas(props.pixiApp.view)
    renderTexture.flipY = false
    texture = renderTexture
  } else {
    // 创建默认纹理
    texture = new THREE.Texture()
    texture.image = createDefaultCanvas()
    texture.needsUpdate = true
  }

  const material = new THREE.MeshLambertMaterial({
    map: texture,
    transparent: true,
    side: THREE.DoubleSide
  })

  modelMesh = new THREE.Mesh(geometry, material)
  modelMesh.castShadow = true
  modelMesh.receiveShadow = true
  
  scene.add(modelMesh)
  
  console.log('模型网格已创建')
}

/**
 * 创建默认画布
 */
function createDefaultCanvas() {
  const canvas = document.createElement('canvas')
  canvas.width = 512
  canvas.height = 512
  const ctx = canvas.getContext('2d')
  
  // 绘制渐变背景
  const gradient = ctx.createLinearGradient(0, 0, 512, 512)
  gradient.addColorStop(0, '#667eea')
  gradient.addColorStop(1, '#764ba2')
  ctx.fillStyle = gradient
  ctx.fillRect(0, 0, 512, 512)
  
  // 绘制文字
  ctx.fillStyle = 'white'
  ctx.font = '32px Arial'
  ctx.textAlign = 'center'
  ctx.fillText('Live2D 3D Mode', 256, 256)
  
  return canvas
}

/**
 * 更新相机位置
 */
function updateCamera() {
  if (!camera) return
  
  const distance = cameraDistance.value
  const height = cameraHeight.value
  
  camera.position.set(0, height, distance)
  camera.lookAt(0, 0, 0)
}

/**
 * 更新模型变换
 */
function updateModelTransform() {
  if (!modelMesh) return
  
  modelMesh.rotation.x = modelRotationX.value
  modelMesh.rotation.y = modelRotationY.value
  modelMesh.rotation.z = modelRotationZ.value
}

/**
 * 更新光照
 */
function updateLighting() {
  if (ambientLight) {
    ambientLight.intensity = ambientLightIntensity.value
  }
  if (directionalLight) {
    directionalLight.intensity = directionalLightIntensity.value
  }
}

/**
 * 开始渲染循环
 */
function startRenderLoop() {
  let frameCount = 0
  let lastFpsTime = performance.now()

  function animate(currentTime) {
    if (!scene || !camera || !renderer) return

    // 计算 FPS
    frameCount++
    if (currentTime - lastFpsTime >= 1000) {
      fps.value = Math.round(frameCount * 1000 / (currentTime - lastFpsTime))
      frameCount = 0
      lastFpsTime = currentTime
    }

    // 环绕动画
    if (isOrbiting.value && camera) {
      const time = currentTime * 0.001
      const radius = cameraDistance.value
      camera.position.x = Math.cos(time) * radius
      camera.position.z = Math.sin(time) * radius
      camera.lookAt(0, 0, 0)
    }

    // 更新 Live2D 纹理（如果可用）
    if (modelMesh && props.pixiApp && props.pixiApp.renderer) {
      const texture = new THREE.CanvasTexture(props.pixiApp.view)
      texture.flipY = false
      modelMesh.material.map = texture
      modelMesh.material.needsUpdate = true
    }

    // 渲染场景
    renderer.render(scene, camera)
    
    animationId = requestAnimationFrame(animate)
  }

  animationId = requestAnimationFrame(animate)
}

/**
 * 开始/停止环绕动画
 */
function startOrbitAnimation() {
  isOrbiting.value = !isOrbiting.value
}

/**
 * 重置视角
 */
function resetView() {
  cameraDistance.value = 15
  cameraHeight.value = 0
  modelRotationX.value = 0
  modelRotationY.value = 0
  modelRotationZ.value = 0
  ambientLightIntensity.value = 0.6
  directionalLightIntensity.value = 1.0
  isOrbiting.value = false
  
  updateCamera()
  updateModelTransform()
  updateLighting()
}

/**
 * 清理资源
 */
function cleanup() {
  console.log('清理 Three.js 资源...')
  
  if (animationId) {
    cancelAnimationFrame(animationId)
    animationId = null
  }
  
  if (scene) {
    scene.clear()
    scene = null
  }
  
  if (renderer) {
    renderer.dispose()
    renderer = null
  }
  
  camera = null
  modelMesh = null
  ambientLight = null
  directionalLight = null
  
  console.log('Three.js 资源清理完成')
}
</script>

<style scoped>
.live2d-threejs-container {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  border-radius: 12px;
  padding: 20px;
  margin: 20px 0;
  color: white;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.control-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.control-header h3 {
  margin: 0;
  font-size: 1.2em;
}

.toggle-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toggle-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.toggle-btn.active {
  background: rgba(255, 255, 255, 0.9);
  color: #1e3c72;
}

.threejs-controls {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.canvas-container {
  position: relative;
}

.threejs-canvas {
  width: 100%;
  max-width: 600px;
  height: 600px;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

.canvas-overlay {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.7);
  padding: 10px;
  border-radius: 6px;
  font-size: 0.9em;
}

.info-panel p {
  margin: 0 0 5px 0;
}

.controls-panel {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.control-group {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 15px;
}

.control-group h4 {
  margin: 0 0 15px 0;
  font-size: 1em;
  opacity: 0.9;
}

.control-item {
  margin-bottom: 15px;
}

.control-item label {
  display: block;
  margin-bottom: 5px;
  font-size: 0.9em;
  opacity: 0.8;
}

.control-item input[type="range"] {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.3);
  outline: none;
  -webkit-appearance: none;
}

.control-item input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: white;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.animation-buttons {
  display: flex;
  gap: 10px;
}

.animation-buttons button {
  flex: 1;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 10px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.animation-buttons button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.animation-buttons button.active {
  background: rgba(255, 255, 255, 0.9);
  color: #1e3c72;
}

.reset-btn {
  background: rgba(255, 87, 87, 0.3) !important;
  border-color: rgba(255, 87, 87, 0.5) !important;
}

.reset-btn:hover {
  background: rgba(255, 87, 87, 0.5) !important;
}

@media (max-width: 768px) {
  .threejs-controls {
    grid-template-columns: 1fr;
  }
}
</style>
