<template>
  <div class="live2d-3d-controls">
    <div class="control-header">
      <h3>🎭 3D 变换控制</h3>
      <button
        @click="toggle3DMode"
        :class="['toggle-btn', { active: is3DMode }]"
      >
        {{ is3DMode ? '🔄 3D模式' : '📱 2D模式' }}
      </button>
    </div>

    <div v-if="is3DMode" class="controls-grid">
      <!-- 旋转控制 -->
      <div class="control-group">
        <h4>🔄 旋转控制</h4>
        
        <div class="control-item">
          <label>X轴旋转 (上下倾斜): {{ rotationX }}°</label>
          <input
            type="range"
            v-model.number="rotationX"
            min="-45"
            max="45"
            step="1"
            @input="apply3DTransform"
          />
        </div>
        
        <div class="control-item">
          <label>Y轴旋转 (左右转动): {{ rotationY }}°</label>
          <input
            type="range"
            v-model.number="rotationY"
            min="-45"
            max="45"
            step="1"
            @input="apply3DTransform"
          />
        </div>
        
        <div class="control-item">
          <label>Z轴旋转 (平面旋转): {{ rotationZ }}°</label>
          <input
            type="range"
            v-model.number="rotationZ"
            min="-180"
            max="180"
            step="1"
            @input="apply3DTransform"
          />
        </div>
      </div>

      <!-- 缩放和位移控制 -->
      <div class="control-group">
        <h4>📏 缩放与位移</h4>
        
        <div class="control-item">
          <label>缩放: {{ scale3D.toFixed(2) }}x</label>
          <input
            type="range"
            v-model.number="scale3D"
            min="0.5"
            max="2.0"
            step="0.1"
            @input="apply3DTransform"
          />
        </div>
        
        <div class="control-item">
          <label>X轴位移: {{ translateX }}px</label>
          <input
            type="range"
            v-model.number="translateX"
            min="-200"
            max="200"
            step="5"
            @input="apply3DTransform"
          />
        </div>
        
        <div class="control-item">
          <label>Y轴位移: {{ translateY }}px</label>
          <input
            type="range"
            v-model.number="translateY"
            min="-200"
            max="200"
            step="5"
            @input="apply3DTransform"
          />
        </div>
      </div>

      <!-- 预设和动画控制 -->
      <div class="control-group">
        <h4>🎬 预设与动画</h4>
        
        <div class="preset-buttons">
          <button @click="applyPreset('front')" class="preset-btn">正面</button>
          <button @click="applyPreset('left')" class="preset-btn">左侧</button>
          <button @click="applyPreset('right')" class="preset-btn">右侧</button>
          <button @click="applyPreset('top')" class="preset-btn">俯视</button>
        </div>
        
        <div class="animation-controls">
          <button
            @click="toggleAutoRotate"
            :class="['anim-btn', { active: isAutoRotating }]"
          >
            {{ isAutoRotating ? '⏸️ 停止旋转' : '🔄 自动旋转' }}
          </button>
          
          <button @click="reset3DTransform" class="reset-btn">
            🔄 重置变换
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

// Props
const props = defineProps({
  is3DMode: Boolean,
  rotationX: Number,
  rotationY: Number,
  rotationZ: Number,
  scale3D: Number,
  translateX: Number,
  translateY: Number,
  translateZ: Number
})

// Emits
const emit = defineEmits([
  'toggle3DMode',
  'apply3DTransform',
  'reset3DTransform',
  'updateRotationX',
  'updateRotationY',
  'updateRotationZ',
  'updateScale3D',
  'updateTranslateX',
  'updateTranslateY',
  'toggleAutoRotate'
])

// 本地状态
const isAutoRotating = ref(false)

// 计算属性 - 使用 v-model 双向绑定
const is3DMode = computed({
  get: () => props.is3DMode,
  set: (value) => emit('toggle3DMode', value)
})

const rotationX = computed({
  get: () => props.rotationX,
  set: (value) => emit('updateRotationX', value)
})

const rotationY = computed({
  get: () => props.rotationY,
  set: (value) => emit('updateRotationY', value)
})

const rotationZ = computed({
  get: () => props.rotationZ,
  set: (value) => emit('updateRotationZ', value)
})

const scale3D = computed({
  get: () => props.scale3D,
  set: (value) => emit('updateScale3D', value)
})

const translateX = computed({
  get: () => props.translateX,
  set: (value) => emit('updateTranslateX', value)
})

const translateY = computed({
  get: () => props.translateY,
  set: (value) => emit('updateTranslateY', value)
})

// 方法
function toggle3DMode() {
  emit('toggle3DMode')
}

function apply3DTransform() {
  emit('apply3DTransform')
}

function reset3DTransform() {
  emit('reset3DTransform')
}

function applyPreset(preset) {
  switch (preset) {
    case 'front':
      rotationX.value = 0
      rotationY.value = 0
      rotationZ.value = 0
      break
    case 'left':
      rotationX.value = 0
      rotationY.value = -25
      rotationZ.value = 0
      break
    case 'right':
      rotationX.value = 0
      rotationY.value = 25
      rotationZ.value = 0
      break
    case 'top':
      rotationX.value = -20
      rotationY.value = 0
      rotationZ.value = 0
      break
  }
  apply3DTransform()
}

function toggleAutoRotate() {
  isAutoRotating.value = !isAutoRotating.value
  emit('toggleAutoRotate', isAutoRotating.value)
}
</script>

<style scoped>
.live2d-3d-controls {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 20px;
  margin: 20px 0;
  color: white;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.control-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.control-header h3 {
  margin: 0;
  font-size: 1.2em;
}

.toggle-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toggle-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.toggle-btn.active {
  background: rgba(255, 255, 255, 0.9);
  color: #667eea;
}

.controls-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.control-group {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 15px;
}

.control-group h4 {
  margin: 0 0 15px 0;
  font-size: 1em;
  opacity: 0.9;
}

.control-item {
  margin-bottom: 15px;
}

.control-item label {
  display: block;
  margin-bottom: 5px;
  font-size: 0.9em;
  opacity: 0.8;
}

.control-item input[type="range"] {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.3);
  outline: none;
  -webkit-appearance: none;
}

.control-item input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: white;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.preset-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  margin-bottom: 15px;
}

.preset-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.preset-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.animation-controls {
  display: flex;
  gap: 10px;
}

.anim-btn, .reset-btn {
  flex: 1;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 10px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.anim-btn:hover, .reset-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.anim-btn.active {
  background: rgba(255, 255, 255, 0.9);
  color: #667eea;
}

.reset-btn {
  background: rgba(255, 87, 87, 0.3);
  border-color: rgba(255, 87, 87, 0.5);
}

.reset-btn:hover {
  background: rgba(255, 87, 87, 0.5);
}
</style>
