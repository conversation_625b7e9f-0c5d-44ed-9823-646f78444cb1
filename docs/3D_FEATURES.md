# Live2D 3D 呈现功能说明

本项目实现了多种 Live2D 3D 呈现方案，让 2D Live2D 模型能够以 3D 效果展示。

## 🎯 功能概述

### 方案一：PIXI.js 3D 变换（基础 3D 效果）
- **实现方式**: 使用 PIXI.js 的变换矩阵模拟 3D 效果
- **优势**: 轻量级，兼容性好，性能优秀
- **适用场景**: 简单的 3D 旋转、缩放、倾斜效果

### 方案二：Three.js 集成（真正的 3D 渲染）
- **实现方式**: 将 Live2D 模型作为纹理应用到 Three.js 3D 场景
- **优势**: 真正的 3D 环境，支持光照、阴影、复杂变换
- **适用场景**: 需要复杂 3D 效果的高级应用

## 🚀 使用方法

### 启动项目
```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 或启动桌面应用
pnpm pet:dev
```

### 基础 3D 变换控制

#### 1. 启用 3D 模式
- 在 Web 界面中找到 "🎭 3D 变换控制" 面板
- 点击 "📱 2D模式" 按钮切换到 "🔄 3D模式"

#### 2. 旋转控制
- **X轴旋转**: 控制模型上下倾斜 (-45° 到 45°)
- **Y轴旋转**: 控制模型左右转动 (-45° 到 45°)  
- **Z轴旋转**: 控制模型平面旋转 (-180° 到 180°)

#### 3. 缩放与位移
- **缩放**: 调整模型大小 (0.5x 到 2.0x)
- **X轴位移**: 水平移动模型 (-200px 到 200px)
- **Y轴位移**: 垂直移动模型 (-200px 到 200px)

#### 4. 预设视角
- **正面**: 重置为正面视角
- **左侧**: 模型向左转 25°
- **右侧**: 模型向右转 25°
- **俯视**: 模型向下倾斜 20°

#### 5. 动画效果
- **自动旋转**: 启用后模型将持续绕 Y 轴旋转
- **重置变换**: 一键恢复所有参数到默认值

### Three.js 高级 3D 渲染

#### 1. 启用 Three.js 模式
- 在 "🌟 Three.js 3D 渲染" 面板中
- 点击 "🖼️ PIXI模式" 切换到 "🎮 Three.js模式"

#### 2. 相机控制
- **相机距离**: 调整观察距离 (5 到 50 单位)
- **相机高度**: 调整观察高度 (-10 到 10 单位)

#### 3. 模型变换
- **X/Y/Z轴旋转**: 精确的弧度制旋转控制 (-π 到 π)
- 支持更精细的旋转调整

#### 4. 光照效果
- **环境光强度**: 调整整体亮度 (0 到 2)
- **方向光强度**: 调整主光源强度 (0 到 3)
- 支持实时阴影渲染

#### 5. 高级动画
- **环绕动画**: 相机围绕模型旋转
- **重置视角**: 恢复默认相机和光照设置

## 🛠️ 技术实现

### PIXI.js 3D 变换原理

```javascript
// 创建变换矩阵
const matrix = new PIXI.Matrix()

// 移动到中心点
matrix.translate(-centerX, -centerY)

// 应用旋转效果
if (rotationX !== 0) {
  const scaleY = Math.cos(radX)
  const skewY = Math.sin(radX) * 0.5
  matrix.scale(1, scaleY)
  matrix.skew(0, skewY)
}

// 应用变换矩阵
model.transform.setFromMatrix(matrix)
```

### Three.js 集成原理

```javascript
// 创建 3D 场景
const scene = new THREE.Scene()
const camera = new THREE.PerspectiveCamera(75, aspect, 0.1, 1000)
const renderer = new THREE.WebGLRenderer()

// 将 PIXI 画布作为纹理
const texture = new THREE.CanvasTexture(pixiApp.view)
const material = new THREE.MeshLambertMaterial({ map: texture })
const geometry = new THREE.PlaneGeometry(8, 10)
const mesh = new THREE.Mesh(geometry, material)

scene.add(mesh)
```

## 📊 性能对比

| 方案 | 性能 | 功能丰富度 | 兼容性 | 文件大小 |
|------|------|------------|--------|----------|
| PIXI.js 3D | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 小 |
| Three.js 3D | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | 大 |

## 🎨 自定义扩展

### 添加新的 3D 效果

1. **扩展变换参数**
```javascript
// 在 App.vue 中添加新的响应式状态
const customEffect = ref(0)

// 在 apply3DTransform 函数中应用效果
function apply3DTransform() {
  // ... 现有代码
  
  // 添加自定义效果
  if (customEffect.value !== 0) {
    // 实现自定义变换逻辑
  }
}
```

2. **添加新的预设**
```javascript
function applyPreset(preset) {
  switch (preset) {
    case 'custom':
      rotationX.value = 15
      rotationY.value = -30
      rotationZ.value = 5
      scale3D.value = 1.2
      break
    // ... 其他预设
  }
}
```

### 集成其他 3D 库

项目架构支持集成其他 3D 渲染库：
- **Babylon.js**: 更强大的 3D 引擎
- **A-Frame**: VR/AR 支持
- **CSS 3D**: 轻量级 CSS 变换

## 🐛 故障排除

### 常见问题

1. **3D 效果不显示**
   - 检查浏览器是否支持 WebGL
   - 确认模型已正确加载
   - 查看控制台错误信息

2. **性能问题**
   - 降低渲染质量设置
   - 关闭不必要的光照效果
   - 减少同时运行的动画

3. **Three.js 纹理更新问题**
   - 确保 PIXI 应用正在运行
   - 检查纹理更新频率设置

### 调试技巧

```javascript
// 启用详细日志
console.log('3D变换已应用:', {
  rotationX: rotationX.value,
  rotationY: rotationY.value,
  rotationZ: rotationZ.value
})

// 检查 Three.js 场景状态
console.log('Three.js 场景对象:', scene.children)
```

## 🔮 未来计划

- [ ] 添加更多预设动画
- [ ] 支持 VR/AR 模式
- [ ] 实现物理引擎集成
- [ ] 添加粒子效果系统
- [ ] 支持多模型 3D 场景

## 📝 更新日志

### v1.0.0 (2024-01-XX)
- ✅ 实现 PIXI.js 基础 3D 变换
- ✅ 集成 Three.js 高级 3D 渲染
- ✅ 添加实时光照和阴影
- ✅ 支持自动旋转动画
- ✅ 提供多种预设视角

---

**注意**: 3D 功能需要现代浏览器支持，建议使用 Chrome 90+ 或 Firefox 88+ 以获得最佳体验。
